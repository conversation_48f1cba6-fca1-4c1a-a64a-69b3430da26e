import * as z from "zod";

// const conditionSchema = z.object({
//   id: z.string(),
//   parentQuestion: z.string().min(1, "Parent question is required"),
//   parentAnswer: z.string().min(1, "Parent question option is required"),
//   operator: z.string().min(1, "Operator is required"),
//   logic: z.string().nullable(),
// });
const conditionSchema = z.object({
  id: z.string(),
  parentQuestion: z.string().optional(),
  parentAnswer: z.string().optional(),
  operator: z.string().optional(),
  logic: z.string().nullable(),
});

const logicSchema = z.object({
  id: z.string(),
  if: z.array(conditionSchema).optional(),
  then: z.string().optional(),
});
// const logicSchema = z.object({
//   id: z.string(),
//   if: z.array(conditionSchema).min(1, "At least one condition is required"),
//   then: z.string().min(1, "Then action is required"),
// });

export const elementLogicSchema = z.object({
  logics: z.array(logicSchema).min(1, "At least one logic is required"),
});
