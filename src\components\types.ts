import { Component } from "@/app/(core)/forms/types";
import { elementLogicSchema } from "@/schemas/logic/logic";
import { type StaticImageData } from "next/image";
import { InputHTMLAttributes, ReactNode } from "react";
import { z } from "zod";

export type TopBarProps = {
  pageTitle?: ReactNode | string;
  organization?: {
    name: string;
    id: string;
    logo: string;
  };
};

export type MenuItems = {
  id: number;
  item: string;
  subItems: {
    id: string;
    icon: StaticImageData;
    title: string;
  }[];
}[];

export type FormNameProps = {
  name: string;
  description?: string;
};

export type MenuItemsOpen = {
  layout: boolean;
  basics: boolean;
};

export type MenuBarElementProps = {
  icon: StaticImageData;
  name: string;
  isDragged?: boolean;
};

export type ElementType =
  // | "SectionLayout"
  | "ShortAnswerField"
  | "ParagraphField"
  | "NumberField"
  | "PhoneNumberField"
  | "DateField"
  | "TimeField"
  | "SingleChoiceField"
  | "MultipleChoiceField"
  | "FileField"
  | "CameraField"
  | "AudioRecordingField"
  | "RatingField"
  | "DropdownField"
  | "ReadOnlyField";

export type ElementProperties =
  | "SectionProperties"
  | "ShortAnswerProperties"
  | "ParagraphProperties"
  | "NumberProperties"
  | "PhoneNumberProperties"
  | "DateProperties"
  | "TimeProperties"
  | "SingleChoiceProperties"
  | "MultipleChoiceProperties"
  | "FileProperties"
  | "CameraProperties"
  | "AudioRecordingProperties"
  | "RatingProperties"
  | "DropdownProperties"
  | "ReadOnlyProperties";

export type FormElement = {
  type: ElementType;
} & Omit<Component, "type">;

export type FormElementWithSectionAndScreen = {
  type: ElementType | "SectionLayout" | "ScreenLayout";
} & Omit<Component, "type">;

export type FormScreen = {
  id: string;
  name: string;
  description: string;
  sections: FormSection[];
};

export type FormSection = {
  id: string;
  title: string;
  elements: (Omit<Component, "type"> & { type: ElementType })[];
};

export type FormElementProperties = Omit<Component, "id" | "type">;

export type FormElementProps = {
  element: FormElement;
  Element: React.FC<BaseFieldProps>;
  screenId: string;
  sectionId: string;
};

export type BaseFieldProps = Omit<FormElementProps, "Element">;

export type FormTooltipProps = {
  description: string;
  side?: "top" | "left" | "right" | "bottom";
};

export type PropertiesBarProps = {
  screenId: string;
};

export type PaginationButtonProps = {
  page: number;
  active?: boolean;
};

export type TablePaginationProps = {
  count: number;
  currentPage: number;
  totalPages: number;
  pageSizes: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
};

export type CreateFormDialogProps = {
  isCreatingBlankForm: boolean;
  handleOpenExistingForm: () => void;
  handleCreateBlankForm: () => void;
};

export type ConfirmationDialogProps = {
  isConfirming: boolean;
  onConfirmation: () => void;
};

export type LogicModalProps = {
  element: string;
  screenId: string;
  sectionId: string;
  operators: string[];
};

export type LabelInputProps = {
  label?: string;
  required?: boolean;
  tooltip?: string;
  onLabelChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
};

export type NumberInputProps = Omit<InputHTMLAttributes<HTMLInputElement>, "onChange"> & {
  value?: number | string;
  onChange?: (value: number | string) => void;
};

export type BasicPropertiesProps = {
  hasHint: boolean;
  hasTooltip: boolean;
  hasRequired: boolean;
  hasValidate: boolean;
  hasTag: boolean;
};

export type ElementLogicData = z.infer<typeof elementLogicSchema>;
