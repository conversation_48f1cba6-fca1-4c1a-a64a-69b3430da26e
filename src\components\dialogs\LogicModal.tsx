import { fredoka } from "@/app/fonts";
import deleteIcon from "@/assets/icons/delete-icon.svg";
import plusIcon from "@/assets/icons/plus-green-outline.svg";
import NumberInput from "@/components/NumberInput";
import { ElementLogicData, FormElement, LogicModalProps } from "@/components/types";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { closeLogicModal } from "@/lib/redux/slices/dialogSlice";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { generateId } from "@/lib/utils";
import { elementLogicSchema } from "@/schemas/logic/logic";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useEffect } from "react";
import { ControllerRenderProps, useFieldArray, useForm } from "react-hook-form";

const LogicModal = ({ element, operators, screenId, sectionId }: LogicModalProps) => {
  const dispatch = useAppDispatch();
  const isLogicModalOpen = useAppSelector(state => state.dialog.isLogicModalOpen);
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;

  const formScreens = useAppSelector(state => state.form.formScreens);

  // const

  const allQuestions = formScreens
    .flatMap(screen => screen.sections)
    .flatMap(section => section.elements)
    .map(element => ({ id: element.id, label: element.label }))
    .filter(element => element.id !== selectedFormBuilderItem.id);

  // Configuration for different field types

  // const logicConfig = getLogicModalConfig(component);
  const logicOptions = ["and", "or"];

  const handleOpenChange = (open: boolean): void => {
    if (!open) {
      dispatch(closeLogicModal());
    }
  };

  const handleAddLogic = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    // appendLogic({
    //   id: `logic_${generateId()}`,
    //   if: [
    //     {
    //       id: `condition_${generateId()}`,
    //       parentQuestion: "",
    //       parentAnswer: "",
    //       operator: "",
    //       logic: null,
    //     },
    //   ],
    //   then: "",
    // });
  };

  const getDefaultValues = () => {
    // If the selected element already has logic, use it; otherwise create a new one
    // if (selectedFormBuilderItem?.logics && selectedFormBuilderItem.logics.length > 0) {
    //   return { logics: selectedFormBuilderItem.logics };
    // }

    return {
      logics: [
        {
          id: `logic_${generateId()}`,
          if: [
            {
              id: `condition_${generateId()}`,
              parentQuestion: "",
              parentAnswer: "",
              operator: operators[0],
              logic: null,
            },
          ],
          then: "Show",
        },
      ],
    };
  };

  const form = useForm<ElementLogicData>({
    resolver: zodResolver(elementLogicSchema),
    mode: "onChange",
    defaultValues: getDefaultValues(),
  });

  const { control, reset } = form;

  // Reset form when modal opens or selected item changes
  useEffect(() => {
    if (isLogicModalOpen) {
      reset(getDefaultValues());
    }
  }, [isLogicModalOpen, selectedFormBuilderItem, reset]);

  const {
    fields: logicFields,
    append: appendLogic,
    remove: removeLogic,
  } = useFieldArray({
    control,
    name: "logics",
  });

  const applyChanges = (data: ElementLogicData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  const renderParentAnswerField = (field: ControllerRenderProps<ElementLogicData, any>) => {
    switch (element) {
      case "Number":
      case "Rating":
        return <NumberInput {...field} placeholder="Enter value" onChange={field.onChange} />;
      case "Single Choice":
      case "Multiple Choice":
      case "Dropdown":
        return (
          <Select onValueChange={field.onChange} value={field.value}>
            <SelectTrigger>
              <SelectValue placeholder="Parent Question Option" className="placeholder:text-red-500" />
            </SelectTrigger>
            <SelectContent>
              {operators.map(option => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
    }
  };

  return (
    <Dialog open={isLogicModalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[40rem] w-[60rem] overflow-auto px-8">
        <DialogHeader>
          <DialogTitle className={`${fredoka.className} text-lg font-semibold`}>Logic</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onChange={form.handleSubmit(applyChanges)} className="space-y-4">
            {logicFields.map((logicField, logicIndex) => {
              const {
                fields: conditionFields,
                append: appendCondition,
                remove: removeCondition,
              } = useFieldArray({
                control: form.control,
                name: `logics.${logicIndex}.if`,
              });

              const handleAddCondition = (e: React.MouseEvent<HTMLButtonElement>) => {
                e.preventDefault();
                appendCondition({
                  id: `condition_${generateId()}`,
                  parentQuestion: "",
                  parentAnswer: "",
                  operator: "",
                  logic: "and",
                });
              };

              const handleDeleteCondition = (e: React.MouseEvent<HTMLButtonElement>, conditionIndex: number) => {
                e.preventDefault();
                removeCondition(conditionIndex);
              };

              return (
                <div key={logicField.id} className="rounded-[10px] border border-[#75748F] p-4">
                  <div className="flex flex-col gap-2">
                    {conditionFields.map((conditionField, conditionIndex) => (
                      <div key={conditionField.id} className="space-y-4">
                        <div className={`w-[10rem] ${conditionField.logic !== null ? "block" : "hidden"}`}>
                          <FormField
                            control={form.control}
                            name={`logics.${logicIndex}.if.${conditionIndex}.logic`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Select onValueChange={field.onChange} value={field.value ?? ""}>
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {logicOptions.map(option => (
                                        <SelectItem key={option} value={option}>
                                          {option}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name={`logics.${logicIndex}.if.${conditionIndex}.parentQuestion`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className={`${conditionIndex === 0 ? "block" : "hidden"}`}>If</FormLabel>
                              <FormControl>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Parent Question" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {allQuestions.map(question => (
                                      <SelectItem key={question.id} value={question.id}>
                                        {question.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <div className="flex gap-4">
                          <div className="w-[20rem]">
                            <FormField
                              control={form.control}
                              name={`logics.${logicIndex}.if.${conditionIndex}.operator`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <SelectTrigger>
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {operators.map((operator: string) => (
                                          <SelectItem key={operator} value={operator}>
                                            {operator}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className="w-full">
                            <FormField
                              control={form.control}
                              name={`logics.${logicIndex}.if.${conditionIndex}.parentAnswer`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    {/* <Select onValueChange={field.onChange} value={field.value}>
                                      <SelectTrigger>
                                        <SelectValue placeholder="Parent Question Option" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {operators.map(option => (
                                          <SelectItem key={option} value={option}>
                                            {option}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select> */}
                                    {renderParentAnswerField(field)}
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                        <div className="flex justify-end gap-3">
                          <Button
                            variant="ghost"
                            className={`cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent ${conditionIndex === conditionFields.length - 1 ? "flex" : "hidden"}`}
                            onClick={handleAddCondition}
                          >
                            <Image src={plusIcon} alt="Plus Icon" width={14} height={14} />
                            Add Condition
                          </Button>
                          <Button
                            variant="ghost"
                            className={`cursor-pointer items-center gap-2 p-0 font-normal hover:bg-transparent ${conditionIndex > 0 && conditionFields.length > 1 ? "flex" : "hidden"}`}
                            onClick={e => handleDeleteCondition(e, conditionIndex)}
                          >
                            <Image src={deleteIcon} alt="Plus Icon" width={14} height={14} />
                            Delete Condition
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Separator className="my-2" />
                    <div className="flex items-center gap-4">
                      <div className="w-[10rem]">
                        <FormField
                          control={form.control}
                          name={`logics.${logicIndex}.then`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Then</FormLabel>
                              <FormControl>
                                <Select onValueChange={field.onChange} value={field.value}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Show">Show</SelectItem>
                                    <SelectItem value="Hide">Hide</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="w-full space-y-2">
                        <FormLabel className="invisible">Dummy</FormLabel>
                        <Input value={selectedFormBuilderItem?.label} readOnly />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </form>
        </Form>
        <div>
          <Button className={`${fredoka.className} font-semibold`} type="button" onClick={handleAddLogic}>
            Add Logic
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LogicModal;
